import { apiService } from '../services/apiService';

/**
 * Check user's current token balance
 */
export async function checkTokenBalance(): Promise<{
  balance: number;
  hasEnoughTokens: boolean;
  message: string;
}> {
  try {
    const response = await apiService.getTokenBalance();
    const balance = response.data?.balance || 0;
    
    return {
      balance,
      hasEnoughTokens: balance >= 2,
      message: balance >= 2 
        ? `You have ${balance} tokens available.`
        : `Insufficient tokens. You have ${balance} tokens but need at least 2 to submit an assessment.`
    };
  } catch (error) {
    console.error('Error checking token balance:', error);
    
    // If we can't check balance, assume user has enough and let API handle it
    return {
      balance: -1,
      hasEnoughTokens: true,
      message: 'Could not check token balance. Proceeding with assessment submission.'
    };
  }
}

/**
 * Display user-friendly error message for token balance issues
 */
export function getTokenBalanceErrorMessage(error: any): string {
  if (error.response?.status === 402) {
    return 'Insufficient token balance. You need at least 2 tokens to submit an assessment. Please contact support or complete more assessments to earn tokens.';
  }
  
  if (error.message?.includes('token balance')) {
    return error.message + ' The system will use local analysis instead.';
  }
  
  return error.message || 'An error occurred while submitting the assessment.';
}
